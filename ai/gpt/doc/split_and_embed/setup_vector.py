import requests
import psycopg2
import uuid

token = None

# Get Token
def get_API_token() -> str:
    global token
    if (token == None):
        client_id = "sb-1a817f6b-f648-447a-af21-eaa44c1fbc97!b93303|azure-openai-service-i057149-xs!b16730"
        client_secret = "6eef4618-ab0d-409e-aef5-ae01afdebce2$HNLTMW-JR35P3vrqe-Jm6G6ZjbU4eu-PoROBHqbql3I="

        params = {"grant_type": "client_credentials"}
        resp = requests.post(f"https://calm-build-poc-llm.authentication.sap.hana.ondemand.com/oauth/token",
                             auth=(client_id, client_secret),
                             params=params)
        token = resp.json()["access_token"]
    return token


# insert data to database
def insert_into_rc_knowledge_base(records: list) -> None:
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="*******************************",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )

        cursor = connection.cursor()
        postgres_insert_query = """ INSERT INTO rc_knowledge_base (id, type, scope, metadata, "pageContent", embedding) VALUES (%s,%s,%s,%s,%s,%s)"""
        
        for record in records: 
            # Assuming each record is a tuple of (type, scope, metadata, pageContent, embedding)
            id = uuid.uuid4()
            updated_record = (str(id),) + record
            cursor.execute(postgres_insert_query, updated_record)

        connection.commit()
        count = cursor.rowcount
        print(count, "Record inserted successfully into rc_knowledge_base table")

    except (Exception, psycopg2.Error) as error:
        if (connection):
            print("Failed to insert record into rc_knowledge_base table", error)

    finally:
        if (connection):
            cursor.close()
            connection.close()
            print("PostgreSQL connection is closed")


def query_from_doc(msg: str) -> str:
    msg_embedding = get_embedding([msg]).json()['data'][0]['embedding']
    result = None
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="*******************************",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )
        cursor = connection.cursor()

        find_text = """ select rc_knowledge_base."pageContent" from test_splitter where embedding <=> %s < 0.3 order by embedding <=> %s limit 3"""

        cursor.execute(find_text, (str(msg_embedding), str(msg_embedding)))
        result = cursor.fetchall()

    except (Exception, psycopg2.Error) as error:
        if (connection):
            print("Failed to query record from test_splitter table", error)

    finally:
        # 关闭数据库连接
        if (connection):
            cursor.close()
            connection.close()

    if result == None or len(result) == 0:
        return ""
    return str(result)


def get_embedding(texts: list[str]) -> requests.Response:
    data = {
        "deployment_id": "text-embedding-ada-002-v2",
        "input": texts
    }

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    svc_url = "https://azure-openai-serv-i057149.cfapps.sap.hana.ondemand.com"

    return requests.post(f"{svc_url}/api/v1/embeddings",
                         headers=headers,
                         json=data)
